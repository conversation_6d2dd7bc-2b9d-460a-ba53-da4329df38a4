#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
飞书多维表格快速开始示例
演示核心功能的使用方法
"""

from feishu_bitable_tools import FeishuBitableTools, FeishuBitableError
from feishu_bitable_config import get_config


def quick_demo():
    """快速演示核心功能"""
    print("🚀 飞书多维表格操作工具 - 快速演示")
    print("=" * 50)
    
    # 获取配置
    config = get_config()
    
    try:
        # 1. 初始化工具
        print("📋 初始化工具...")
        bitable_tools = FeishuBitableTools(
            app_id=config["APP_ID"],
            app_secret=config["APP_SECRET"],
            log_name=config["LOG_NAME"]
        )
        print("✅ 工具初始化成功")
        
        # 2. 解析URL
        print("\n🔍 解析多维表格URL...")
        url = config["DEFAULT_BITABLE_URL"]
        url_params = bitable_tools.parse_bitable_url(url)
        print(f"✅ URL解析成功:")
        print(f"   📊 App Token: {url_params['app_token']}")
        print(f"   📋 Table ID: {url_params['table_id']}")
        print(f"   👁️ View ID: {url_params['view_id']}")
        
        # 3. 生成分享链接示例
        print("\n🔗 生成分享链接示例...")
        sample_record_id = "recHSoEbNv"  # 示例记录ID
        share_link = bitable_tools.generate_record_share_link(url, sample_record_id)
        print(f"✅ 分享链接生成成功:")
        print(f"   🔗 {share_link}")
        
        print("\n" + "=" * 50)
        print("🎉 快速演示完成！")
        print("\n💡 提示:")
        print("   1. 请在 feishu_bitable_config.py 中配置您的实际应用信息")
        print("   2. 使用真实的应用ID和密钥才能访问实际数据")
        print("   3. 查看 feishu_bitable_usage_example.py 了解更多功能")
        
    except FeishuBitableError as e:
        print(f"❌ 飞书操作错误: {e}")
    except Exception as e:
        print(f"❌ 未知错误: {e}")


def show_usage_examples():
    """显示使用示例"""
    print("\n📚 常用功能示例代码:")
    print("-" * 30)
    
    examples = [
        {
            "title": "1. 获取Level为L0的记录",
            "code": '''
records = bitable_tools.get_records_by_condition(
    url=url,
    field_name="Level", 
    field_value="L0",
    operator="is"
)
print(f"找到 {len(records)} 条记录")
'''
        },
        {
            "title": "2. 多条件搜索",
            "code": '''
conditions = [
    {"field_name": "Level", "operator": "is", "value": "L0"},
    {"field_name": "Status", "operator": "isNot", "value": "Deleted"}
]
records = bitable_tools.search_records_by_multiple_conditions(
    url=url, 
    conditions=conditions, 
    conjunction="and"
)
'''
        },
        {
            "title": "3. 生成记录分享链接",
            "code": '''
for record in records:
    record_id = record['record_id']
    share_link = bitable_tools.generate_record_share_link(url, record_id)
    print(f"分享链接: {share_link}")
'''
        },
        {
            "title": "4. 获取所有记录",
            "code": '''
all_records = bitable_tools.get_all_records(url)
print(f"总记录数: {len(all_records)}")
'''
        }
    ]
    
    for example in examples:
        print(f"\n{example['title']}:")
        print(example['code'])


def show_supported_operators():
    """显示支持的操作符"""
    print("\n🔧 支持的筛选操作符:")
    print("-" * 30)
    
    operators = {
        "is": "等于",
        "isNot": "不等于",
        "contains": "包含", 
        "doesNotContain": "不包含",
        "isEmpty": "为空",
        "isNotEmpty": "不为空",
        "isGreater": "大于",
        "isGreaterEqual": "大于等于", 
        "isLess": "小于",
        "isLessEqual": "小于等于"
    }
    
    for op, desc in operators.items():
        print(f"   {op:<15} - {desc}")


def main():
    """主函数"""
    # 运行快速演示
    quick_demo()
    
    # 显示使用示例
    show_usage_examples()
    
    # 显示支持的操作符
    show_supported_operators()
    
    print("\n" + "=" * 50)
    print("📖 更多信息请查看:")
    print("   - feishu_bitable_tools_README.md (详细文档)")
    print("   - feishu_bitable_usage_example.py (完整示例)")
    print("   - feishu_bitable_config.py (配置文件)")


if __name__ == "__main__":
    main()
